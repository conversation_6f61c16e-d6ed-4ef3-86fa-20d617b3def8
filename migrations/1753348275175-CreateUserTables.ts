import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateUserTables1753348275175 implements MigrationInterface {
  name = 'CreateUserTables1753348275175';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE \`ai_assistant_user_global_config\` (\`id\` int NOT NULL AUTO_INCREMENT COMMENT '主键ID', \`create_time\` datetime(6) NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP(6), \`update_time\` datetime(6) NOT NULL COMMENT '更新时间' DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`is_deleted\` tinyint NOT NULL COMMENT '是否删除（软删除标记）' DEFAULT 0, \`user_id\` int NOT NULL COMMENT '外键，关联用户基本信息表', \`translate_source_language\` varchar(10) NOT NULL COMMENT '翻译源语言' DEFAULT 'auto', \`translate_target_language\` varchar(10) NOT NULL COMMENT '翻译目标语言' DEFAULT 'zh-CN', \`floating_ball_expanded\` tinyint NOT NULL COMMENT '悬浮球是否展开' DEFAULT 0, \`blocked_websites\` json NULL COMMENT '禁用网站URL数组（JSON格式存储）', UNIQUE INDEX \`REL_e5330455803d6a919beee66e3f\` (\`user_id\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`
    );
    await queryRunner.query(
      `CREATE TABLE \`ai_assistant_user_base_info\` (\`id\` int NOT NULL AUTO_INCREMENT COMMENT '主键ID', \`create_time\` datetime(6) NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP(6), \`update_time\` datetime(6) NOT NULL COMMENT '更新时间' DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`is_deleted\` tinyint NOT NULL COMMENT '是否删除（软删除标记）' DEFAULT 0, \`username\` varchar(100) NOT NULL COMMENT '用户名', \`password_hash\` varchar(255) NOT NULL COMMENT '密码哈希值', \`email\` varchar(100) NULL COMMENT '邮箱', \`avatar_url\` varchar(500) NULL COMMENT '头像URL', PRIMARY KEY (\`id\`)) ENGINE=InnoDB`
    );
    await queryRunner.query(
      `CREATE TABLE \`ai_assistant_user_skill_config\` (\`id\` int NOT NULL AUTO_INCREMENT COMMENT '主键ID', \`create_time\` datetime(6) NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP(6), \`update_time\` datetime(6) NOT NULL COMMENT '更新时间' DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`is_deleted\` tinyint NOT NULL COMMENT '是否删除（软删除标记）' DEFAULT 0, \`user_id\` int NOT NULL COMMENT '外键，关联用户基本信息表', \`skill_name\` varchar(100) NOT NULL COMMENT '技能名称', \`skill_description\` text NULL COMMENT '技能描述', \`skill_prompt\` text NOT NULL COMMENT '技能prompt模板', \`status\` int NOT NULL COMMENT '技能状态 (0-禁用，1-启用)' DEFAULT '1', PRIMARY KEY (\`id\`)) ENGINE=InnoDB`
    );
    await queryRunner.query(
      `ALTER TABLE \`ai_assistant_user_global_config\` ADD CONSTRAINT \`FK_e5330455803d6a919beee66e3ff\` FOREIGN KEY (\`user_id\`) REFERENCES \`ai_assistant_user_base_info\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`ai_assistant_user_global_config\` DROP FOREIGN KEY \`FK_e5330455803d6a919beee66e3ff\``
    );
    await queryRunner.query(`DROP TABLE \`ai_assistant_user_skill_config\``);
    await queryRunner.query(`DROP TABLE \`ai_assistant_user_base_info\``);
    await queryRunner.query(
      `DROP INDEX \`REL_e5330455803d6a919beee66e3f\` ON \`ai_assistant_user_global_config\``
    );
    await queryRunner.query(`DROP TABLE \`ai_assistant_user_global_config\``);
  }
}
