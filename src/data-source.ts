import { DataSource } from 'typeorm';
import { SnakeNamingStrategy } from 'typeorm-naming-strategies';
import config from './common/configs';

const appConfig = config();

export const AppDataSource = new DataSource({
  type: 'mysql',
  host: appConfig.mysql?.host,
  port: appConfig.mysql?.port,
  username: appConfig.mysql?.username,
  password: appConfig.mysql?.password,
  database: appConfig.mysql?.database,
  entities: ['src/**/*.entity{.ts,.js}'],
  migrations: ['migrations/*{.ts,.js}'],
  synchronize: false,
  logging: true,
  namingStrategy: new SnakeNamingStrategy(),
});
