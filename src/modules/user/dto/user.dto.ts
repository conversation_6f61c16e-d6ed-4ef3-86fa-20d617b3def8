/* eslint-disable max-classes-per-file */
import { ApiProperty, PartialType } from '@nestjs/swagger';
import {
  IsString,
  IsEmail,
  IsOptional,
  IsNotEmpty,
  Length,
  IsUrl,
  IsInt,
  Min,
  Max,
  IsArray,
  IsBoolean,
} from 'class-validator';

// 统一的用户创建DTO - 支持只传userId或完整信息
export class CreateUserDto {
  @ApiProperty({ description: '用户ID', example: 'user_123456' })
  @IsNotEmpty({ message: '用户ID不能为空' })
  @IsString({ message: '用户ID必须是字符串' })
  @Length(1, 100, { message: '用户ID长度必须在1-100之间' })
  userId: string;

  @ApiProperty({
    description: '用户名',
    example: 'zhangsan',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '用户名必须是字符串' })
  @Length(1, 100, { message: '用户名长度必须在1-100之间' })
  username?: string;

  @ApiProperty({
    description: '密码哈希值',
    example: 'hashed_password',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '密码哈希值必须是字符串' })
  @Length(1, 255, { message: '密码哈希值长度必须在1-255之间' })
  passwordHash?: string;

  @ApiProperty({
    description: '邮箱',
    example: '<EMAIL>',
    required: false,
  })
  @IsOptional()
  @IsEmail({}, { message: '邮箱格式不正确' })
  @Length(1, 100, { message: '邮箱长度必须在1-100之间' })
  email?: string;

  @ApiProperty({ description: '头像URL', required: false })
  @IsOptional()
  @IsUrl({}, { message: '头像URL格式不正确' })
  @Length(1, 500, { message: '头像URL长度必须在1-500之间' })
  avatarUrl?: string;
}

export class UpdateUserDto extends PartialType(CreateUserDto) {}

// 用户配置相关 DTO
export class CreateUserConfigDto {
  @ApiProperty({ description: '翻译源语言', example: 'auto', required: false })
  @IsOptional()
  @IsString({ message: '翻译源语言必须是字符串' })
  @Length(1, 10, { message: '翻译源语言长度必须在1-10之间' })
  sourceLanguage?: string;

  @ApiProperty({
    description: '翻译目标语言',
    example: 'zh-CN',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '翻译目标语言必须是字符串' })
  @Length(1, 10, { message: '翻译目标语言长度必须在1-10之间' })
  targetLanguage?: string;

  @ApiProperty({
    description: '悬浮球是否展开',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: '悬浮球展开状态必须是布尔值' })
  floatingBallExpanded?: boolean;

  @ApiProperty({
    description: '禁用网站URL数组',
    example: ['https://example.com'],
    required: false,
  })
  @IsOptional()
  @IsArray({ message: '禁用网站必须是数组' })
  @IsString({ each: true, message: '禁用网站URL必须是字符串' })
  blockedWebsites?: string[];
}

export class UpdateUserConfigDto extends PartialType(CreateUserConfigDto) {}

// 获取用户配置的请求DTO
export class GetUserConfigDto {
  @ApiProperty({ description: '用户ID', example: 'user_123456' })
  @IsNotEmpty({ message: '用户ID不能为空' })
  @IsString({ message: '用户ID必须是字符串' })
  userId: string;
}

// 更新用户配置的请求DTO
export class UpdateUserConfigRequestDto {
  @ApiProperty({ description: '用户ID', example: 'user_123456' })
  @IsNotEmpty({ message: '用户ID不能为空' })
  @IsString({ message: '用户ID必须是字符串' })
  userId: string;

  @ApiProperty({ description: '翻译源语言', example: 'auto', required: false })
  @IsOptional()
  @IsString({ message: '翻译源语言必须是字符串' })
  @Length(1, 10, { message: '翻译源语言长度必须在1-10之间' })
  sourceLanguage?: string;

  @ApiProperty({
    description: '翻译目标语言',
    example: 'zh-CN',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '翻译目标语言必须是字符串' })
  @Length(1, 10, { message: '翻译目标语言长度必须在1-10之间' })
  targetLanguage?: string;

  @ApiProperty({
    description: '悬浮球是否展开',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: '悬浮球展开状态必须是布尔值' })
  floatingBallExpanded?: boolean;

  @ApiProperty({
    description: '禁用网站URL数组',
    example: ['https://example.com'],
    required: false,
  })
  @IsOptional()
  @IsArray({ message: '禁用网站必须是数组' })
  @IsString({ each: true, message: '禁用网站URL必须是字符串' })
  blockedWebsites?: string[];
}

// 用户技能相关 DTO
export class CreateUserSkillDto {
  @ApiProperty({ description: '技能名称', example: '翻译助手' })
  @IsNotEmpty({ message: '技能名称不能为空' })
  @IsString({ message: '技能名称必须是字符串' })
  @Length(1, 100, { message: '技能名称长度必须在1-100之间' })
  skillName: string;

  @ApiProperty({
    description: '技能描述',
    example: '帮助用户进行多语言翻译',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '技能描述必须是字符串' })
  skillDescription?: string;

  @ApiProperty({
    description: '技能prompt模板',
    example: '请将以下内容翻译为{targetLanguage}：{content}',
  })
  @IsNotEmpty({ message: '技能prompt模板不能为空' })
  @IsString({ message: '技能prompt模板必须是字符串' })
  skillPrompt: string;

  @ApiProperty({
    description: '技能状态 (0-禁用，1-启用)',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsInt({ message: '技能状态必须是整数' })
  @Min(0, { message: '技能状态值不能小于0' })
  @Max(1, { message: '技能状态值不能大于1' })
  status?: number;
}

export class UpdateUserSkillDto extends PartialType(CreateUserSkillDto) {}
