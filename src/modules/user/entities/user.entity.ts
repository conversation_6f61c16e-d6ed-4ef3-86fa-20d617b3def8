import { Entity, Column } from 'typeorm';
import { Base } from '../../../common/model/base.model';

@Entity('ai_assistant_user_base_info')
export class User extends Base {
  @Column({
    name: 'user_id',
    type: 'varchar',
    length: 255,
    unique: true,
    comment: '用户ID',
  })
  userId: string;

  @Column({
    name: 'username',
    length: 100,
    nullable: true,
    default: '匿名用户',
    comment: '用户名',
  })
  username?: string;

  @Column({
    name: 'password_hash',
    length: 255,
    nullable: true,
    comment: '密码哈希值',
  })
  passwordHash?: string;

  @Column({ name: 'email', length: 100, nullable: true, comment: '邮箱' })
  email?: string;

  @Column({
    name: 'avatar_url',
    length: 500,
    nullable: true,
    comment: '头像URL',
  })
  avatarUrl?: string;
}
