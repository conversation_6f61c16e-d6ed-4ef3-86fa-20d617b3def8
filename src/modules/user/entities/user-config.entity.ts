import { Entity, Column } from 'typeorm';
import { Base } from '../../../common/model/base.model';

@Entity('ai_assistant_user_global_config')
export class UserConfig extends Base {
  @Column({
    name: 'user_id',
    type: 'varchar',
    length: 255,
    comment: '用户ID',
  })
  userId: string;

  @Column({
    name: 'translate_source_language',
    length: 10,
    default: 'auto',
    comment: '翻译源语言',
  })
  sourceLanguage: string;

  @Column({
    name: 'translate_target_language',
    length: 10,
    default: 'zh-CN',
    comment: '翻译目标语言',
  })
  targetLanguage: string;

  @Column({
    name: 'floating_ball_expanded',
    type: 'boolean',
    default: false,
    comment: '悬浮球是否展开',
  })
  floatingBallExpanded: boolean;

  @Column({
    name: 'blocked_websites',
    type: 'json',
    nullable: true,
    comment: '禁用网站URL数组（JSON格式存储）',
  })
  blockedWebsites?: string[];
}
