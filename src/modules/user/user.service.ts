import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { User } from './entities/user.entity';
import { UserConfig } from './entities/user-config.entity';
import { UserSkill } from './entities/user-skill.entity';
import { CreateUserDto, UpdateUserConfigRequestDto } from './dto';

@Injectable()
export class UserService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(UserConfig)
    private readonly userConfigRepository: Repository<UserConfig>,
    @InjectRepository(UserSkill)
    private readonly userSkillRepository: Repository<UserSkill>
  ) {}

  // 统一的用户创建服务 - 支持只传userId或完整信息
  async createUser(createUserDto: CreateUserDto): Promise<User> {
    // 检查用户是否已存在
    const existingUser = await this.userRepository.findOne({
      where: { userId: createUserDto.userId },
    });

    if (existingUser) {
      // 如果用户已存在，直接返回提示信息，不做更新
      throw new Error('用户已存在');
    }

    // 创建新用户
    const user = this.userRepository.create(createUserDto);
    const savedUser = await this.userRepository.save(user);

    // 创建用户的默认配置
    await this.createDefaultUserConfig(createUserDto.userId);

    return savedUser;
  }

  // 创建用户默认配置
  private async createDefaultUserConfig(userId: string): Promise<UserConfig> {
    const defaultConfig = this.userConfigRepository.create({
      userId,
      sourceLanguage: 'auto',
      targetLanguage: 'zh-CN',
      floatingBallExpanded: false,
      blockedWebsites: [],
    });

    return this.userConfigRepository.save(defaultConfig);
  }

  // 根据userId获取用户配置信息
  async getUserConfig(userId: string): Promise<UserConfig | null> {
    return this.userConfigRepository.findOne({
      where: { userId },
    });
  }

  // 更新用户配置信息
  async updateUserConfig(
    updateData: UpdateUserConfigRequestDto
  ): Promise<UserConfig> {
    const { userId, ...configData } = updateData;

    // 查找现有配置
    let userConfig = await this.userConfigRepository.findOne({
      where: { userId },
    });

    if (!userConfig) {
      // 如果配置不存在，创建新的配置
      userConfig = this.userConfigRepository.create({
        userId,
        ...configData,
        // 设置默认值
        sourceLanguage: configData.sourceLanguage || 'auto',
        targetLanguage: configData.targetLanguage || 'zh-CN',
        floatingBallExpanded: configData.floatingBallExpanded || false,
        blockedWebsites: configData.blockedWebsites || [],
      });
    } else {
      // 更新现有配置
      Object.assign(userConfig, configData);
    }

    return this.userConfigRepository.save(userConfig);
  }
}
