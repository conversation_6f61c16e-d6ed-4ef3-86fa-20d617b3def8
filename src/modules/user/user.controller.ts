import { Controller, Post, Body } from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';

import { Res } from 'src/common/dto/res.dto';
import { ErrorCode } from 'src/error/error-code';

import { UserService } from './user.service';
import {
  CreateUserDto,
  GetUserConfigDto,
  UpdateUserConfigRequestDto,
} from './dto';

@ApiTags('用户管理')
@Controller('/v1/user')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Post('/createUser')
  @ApiOperation({
    summary: '创建用户',
    description:
      '支持只传userId创建用户（会自动创建默认配置），也支持传完整用户信息',
  })
  async createUser(@Body() createUserDto: CreateUserDto) {
    try {
      const user = await this.userService.createUser(createUserDto);
      return new Res().success(user);
    } catch (error) {
      return new Res().error(new ErrorCode().DEMO_UNKNOWN, error.message);
    }
  }

  @Post('/getUserConfig')
  @ApiOperation({
    summary: '根据user_id获取用户配置信息',
    description:
      '获取用户的全局配置信息，包括翻译设置、悬浮球状态等。如果用户不存在，会自动创建用户并返回默认配置',
  })
  async getUserConfig(@Body() getUserConfigDto: GetUserConfigDto) {
    try {
      let config = await this.userService.getUserConfig(
        getUserConfigDto.userId
      );

      // 如果用户配置不存在，自动创建用户并获取默认配置
      if (!config) {
        await this.userService.createUser({ userId: getUserConfigDto.userId });
        config = await this.userService.getUserConfig(getUserConfigDto.userId);
      }

      return new Res().success(config);
    } catch (error) {
      return new Res().error(new ErrorCode().DEMO_UNKNOWN, error.message);
    }
  }

  @Post('/updateUserConfig')
  @ApiOperation({
    summary: '更新用户配置信息',
    description: '更新用户的全局配置信息，支持部分字段更新',
  })
  async updateUserConfig(
    @Body() updateUserConfigDto: UpdateUserConfigRequestDto
  ) {
    try {
      const config = await this.userService.updateUserConfig(
        updateUserConfigDto
      );
      return new Res().success(config);
    } catch (error) {
      return new Res().error(new ErrorCode().DEMO_UNKNOWN, error.message);
    }
  }
}
